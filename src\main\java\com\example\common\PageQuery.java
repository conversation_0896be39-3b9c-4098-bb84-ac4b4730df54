package com.example.common;

/**
 * 分页查询参数
 */
public class PageQuery {

    private Integer page = 1;
    private Integer size = 10;
    private String keyword;

    public PageQuery() {}

    public PageQuery(Integer page, Integer size) {
        this.page = page;
        this.size = size;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return "PageQuery{" +
                "page=" + page +
                ", size=" + size +
                ", keyword='" + keyword + '\'' +
                '}';
    }
}
