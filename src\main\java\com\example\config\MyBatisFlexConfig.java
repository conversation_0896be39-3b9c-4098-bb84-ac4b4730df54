package com.example.config;

import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Flex 配置类
 */
@Configuration
public class MyBatisFlexConfig {

    @Bean
    public MyBatisFlexCustomizer myBatisFlexCustomizer() {
        return new MyBatisFlexCustomizer() {
            @Override
            public void customize(com.mybatisflex.core.FlexGlobalConfig globalConfig) {
                // 开启审计功能
                AuditManager.setAuditEnable(true);
            }
        };
    }
}
