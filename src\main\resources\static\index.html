<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MyBatis-Flex CRUD 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        input, select, textarea {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .api-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MyBatis-Flex CRUD 测试页面</h1>
        <p>本页面提供了多种形式的API测试接口，包括表单提交、JSON提交、文件上传等。</p>

        <!-- 查询用户 -->
        <div class="section">
            <h3>1. 查询用户 (GET 请求参数)</h3>
            <div class="api-info">GET /api/users?page=1&size=10&keyword=张</div>
            <div class="form-group">
                <label>页码:</label>
                <input type="number" id="queryPage" value="1" min="1">
            </div>
            <div class="form-group">
                <label>页大小:</label>
                <input type="number" id="querySize" value="10" min="1">
            </div>
            <div class="form-group">
                <label>关键词:</label>
                <input type="text" id="queryKeyword" placeholder="用户名关键词">
            </div>
            <button onclick="queryUsers()">查询用户</button>
            <div class="result" id="queryResult"></div>
        </div>

        <!-- JSON搜索用户 -->
        <div class="section">
            <h3>2. 搜索用户 (POST JSON)</h3>
            <div class="api-info">POST /api/users/search</div>
            <div class="form-group">
                <label>JSON数据:</label>
                <textarea id="searchJson" rows="4" style="width: 400px;">{"page": 1, "size": 10, "keyword": ""}</textarea>
            </div>
            <button onclick="searchUsers()">搜索用户</button>
            <div class="result" id="searchResult"></div>
        </div>

        <!-- 创建用户 - 表单 -->
        <div class="section">
            <h3>3. 创建用户 (表单提交)</h3>
            <div class="api-info">POST /api/users/form</div>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="formName" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label>年龄:</label>
                <input type="number" id="formAge" placeholder="请输入年龄" min="1" max="150">
            </div>
            <div class="form-group">
                <label>部门ID:</label>
                <input type="number" id="formDeptId" placeholder="部门ID">
            </div>
            <div class="form-group">
                <label>状态:</label>
                <select id="formStatus">
                    <option value="0">正常</option>
                    <option value="1">禁用</option>
                </select>
            </div>
            <button onclick="createUserByForm()">创建用户(表单)</button>
            <div class="result" id="formCreateResult"></div>
        </div>

        <!-- 创建用户 - JSON -->
        <div class="section">
            <h3>4. 创建用户 (JSON提交)</h3>
            <div class="api-info">POST /api/users</div>
            <div class="form-group">
                <label>JSON数据:</label>
                <textarea id="createJson" rows="6" style="width: 400px;">{"name": "测试用户", "age": 25, "deptId": 1, "status": 0}</textarea>
            </div>
            <button onclick="createUserByJson()">创建用户(JSON)</button>
            <div class="result" id="jsonCreateResult"></div>
        </div>

        <!-- 文件导入 -->
        <div class="section">
            <h3>5. 文件导入 (文件上传)</h3>
            <div class="api-info">POST /api/users/import</div>
            <div class="form-group">
                <label>Excel文件:</label>
                <input type="file" id="importFile" accept=".xlsx,.xls">
            </div>
            <button onclick="importUsers()">导入用户</button>
            <button onclick="downloadTemplate()">下载模板</button>
            <div class="result" id="importResult"></div>
        </div>

        <!-- 导出数据 -->
        <div class="section">
            <h3>6. 导出数据</h3>
            <div class="api-info">GET /api/users/export</div>
            <button onclick="exportUsers()">导出用户数据</button>
            <div class="result" id="exportResult"></div>
        </div>

        <!-- 部门管理 -->
        <div class="section">
            <h3>7. 部门管理</h3>
            <div class="api-info">GET /api/departments/all</div>
            <button onclick="getAllDepartments()">查询所有部门</button>
            <div class="result" id="deptResult"></div>
        </div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8080';

        // 查询用户
        function queryUsers() {
            const page = document.getElementById('queryPage').value;
            const size = document.getElementById('querySize').value;
            const keyword = document.getElementById('queryKeyword').value;
            
            let url = `${baseUrl}/api/users?page=${page}&size=${size}`;
            if (keyword) {
                url += `&keyword=${encodeURIComponent(keyword)}`;
            }
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('queryResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('queryResult').textContent = '错误: ' + error.message;
                });
        }

        // 搜索用户
        function searchUsers() {
            const jsonData = document.getElementById('searchJson').value;
            
            fetch(`${baseUrl}/api/users/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: jsonData
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('searchResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('searchResult').textContent = '错误: ' + error.message;
                });
        }

        // 表单创建用户
        function createUserByForm() {
            const formData = new FormData();
            formData.append('name', document.getElementById('formName').value);
            formData.append('age', document.getElementById('formAge').value);
            formData.append('deptId', document.getElementById('formDeptId').value);
            formData.append('status', document.getElementById('formStatus').value);
            
            fetch(`${baseUrl}/api/users/form`, {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('formCreateResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('formCreateResult').textContent = '错误: ' + error.message;
                });
        }

        // JSON创建用户
        function createUserByJson() {
            const jsonData = document.getElementById('createJson').value;
            
            fetch(`${baseUrl}/api/users`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: jsonData
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('jsonCreateResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('jsonCreateResult').textContent = '错误: ' + error.message;
                });
        }

        // 导入用户
        function importUsers() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];
            
            if (!file) {
                document.getElementById('importResult').textContent = '请选择文件';
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            fetch(`${baseUrl}/api/users/import`, {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('importResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('importResult').textContent = '错误: ' + error.message;
                });
        }

        // 导出用户
        function exportUsers() {
            window.open(`${baseUrl}/api/users/export`, '_blank');
            document.getElementById('exportResult').textContent = '导出请求已发送，请检查下载';
        }

        // 下载模板
        function downloadTemplate() {
            // 创建一个简单的Excel模板下载链接
            const csvContent = "用户名,年龄,部门ID,状态\n张三,25,1,0\n李四,30,2,0";
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement("a");
            const url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", "用户导入模板.csv");
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 查询所有部门
        function getAllDepartments() {
            fetch(`${baseUrl}/api/departments/all`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('deptResult').textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('deptResult').textContent = '错误: ' + error.message;
                });
        }
    </script>
</body>
</html>
