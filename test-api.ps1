# MyBatis-Flex CRUD API 测试脚本
# 测试各种参数形式的接口

$baseUrl = "http://localhost:8080"

Write-Host "=== MyBatis-Flex CRUD API 测试 ===" -ForegroundColor Green

# 1. 测试查询所有部门
Write-Host "`n1. 测试查询所有部门 (GET)" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/departments/all" -Method GET
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content | ConvertFrom-Json
    Write-Host "返回数据: $($content.data.Count) 个部门" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试分页查询用户 (GET 参数)
Write-Host "`n2. 测试分页查询用户 (GET 参数)" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/api/users?page=1`&size=5" -Method GET
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content | ConvertFrom-Json
    Write-Host "返回数据: 第$($content.data.pageNumber)页，共$($content.data.totalRow)条记录" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试搜索用户 (POST JSON)
Write-Host "`n3. 测试搜索用户 (POST JSON)" -ForegroundColor Yellow
try {
    $body = @{
        page = 1
        size = 10
        keyword = ""
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/api/users/search" -Method POST -Body $body -ContentType "application/json"
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content | ConvertFrom-Json
    Write-Host "返回数据: 第$($content.data.pageNumber)页，共$($content.data.totalRow)条记录" -ForegroundColor Green
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试创建用户 (JSON)
Write-Host "`n4. 测试创建用户 (POST JSON)" -ForegroundColor Yellow
try {
    $body = @{
        name = "测试用户$(Get-Random -Maximum 1000)"
        age = 25
        deptId = 1
        status = 0
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "$baseUrl/api/users" -Method POST -Body $body -ContentType "application/json"
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content | ConvertFrom-Json
    Write-Host "创建成功: 用户ID $($content.data.id)，姓名 $($content.data.name)" -ForegroundColor Green
    $global:testUserId = $content.data.id
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试创建部门 (表单)
Write-Host "`n5. 测试创建部门 (POST 表单)" -ForegroundColor Yellow
try {
    $formData = @{
        name = "测试部门$(Get-Random -Maximum 1000)"
        parentId = 1
    }
    
    $response = Invoke-WebRequest -Uri "$baseUrl/api/departments/form" -Method POST -Body $formData
    Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
    $content = $response.Content | ConvertFrom-Json
    Write-Host "创建成功: 部门ID $($content.data.id)，名称 $($content.data.name)" -ForegroundColor Green
    $global:testDeptId = $content.data.id
} catch {
    Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试更新用户 (JSON)
if ($global:testUserId) {
    Write-Host "`n6. 测试更新用户 (PUT JSON)" -ForegroundColor Yellow
    try {
        $body = @{
            name = "更新后的用户"
            age = 30
            deptId = 2
            status = 0
        } | ConvertTo-Json
        
        $response = Invoke-WebRequest -Uri "$baseUrl/api/users/$($global:testUserId)" -Method PUT -Body $body -ContentType "application/json"
        Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
        $content = $response.Content | ConvertFrom-Json
        Write-Host "更新成功: $($content.message)" -ForegroundColor Green
    } catch {
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 7. 测试查询单个用户
if ($global:testUserId) {
    Write-Host "`n7. 测试查询单个用户 (GET 路径参数)" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/api/users/$($global:testUserId)" -Method GET
        Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
        $content = $response.Content | ConvertFrom-Json
        Write-Host "查询成功: 用户 $($content.data.name)，年龄 $($content.data.age)" -ForegroundColor Green
    } catch {
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 8. 测试删除用户
if ($global:testUserId) {
    Write-Host "`n8. 测试删除用户 (DELETE)" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/api/users/$($global:testUserId)" -Method DELETE
        Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
        $content = $response.Content | ConvertFrom-Json
        Write-Host "删除成功: $($content.data)" -ForegroundColor Green
    } catch {
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 9. 测试删除部门
if ($global:testDeptId) {
    Write-Host "`n9. 测试删除部门 (DELETE)" -ForegroundColor Yellow
    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/api/departments/$($global:testDeptId)" -Method DELETE
        Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green
        $content = $response.Content | ConvertFrom-Json
        Write-Host "删除成功: $($content.data)" -ForegroundColor Green
    } catch {
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "请访问 http://localhost:8080 查看完整的测试页面" -ForegroundColor Cyan
