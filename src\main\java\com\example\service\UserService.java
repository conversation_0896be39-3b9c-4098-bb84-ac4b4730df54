package com.example.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.example.dto.UserDTO;
import com.example.entity.User;
import com.example.mapper.UserMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

import static com.example.entity.table.UserTableDef.USER;

/**
 * 用户服务类
 */
@Service
public class UserService {

    @Autowired
    private UserMapper userMapper;

    /**
     * 分页查询用户
     */
    public Page<User> pageUsers(int page, int size, String keyword) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where(USER.IS_DELETED.eq(0));
        
        if (StrUtil.isNotBlank(keyword)) {
            queryWrapper.and(USER.NAME.like(keyword));
        }
        
        return userMapper.paginate(page, size, queryWrapper);
    }

    /**
     * 根据ID查询用户
     */
    public User getUserById(Long id) {
        return userMapper.selectOneById(id);
    }

    /**
     * 创建用户
     */
    public User createUser(UserDTO userDTO) {
        User user = new User();
        BeanUtil.copyProperties(userDTO, user);
        userMapper.insert(user);
        return user;
    }

    /**
     * 更新用户
     */
    public User updateUser(Long id, UserDTO userDTO) {
        User user = userMapper.selectOneById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        BeanUtil.copyProperties(userDTO, user, "id");
        userMapper.update(user);
        return user;
    }

    /**
     * 删除用户（逻辑删除）
     */
    public void deleteUser(Long id) {
        User user = userMapper.selectOneById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        user.setIsDeleted(1);
        userMapper.update(user);
    }

    /**
     * 批量删除用户
     */
    public void batchDeleteUsers(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        
        for (Long id : ids) {
            deleteUser(id);
        }
    }

    /**
     * 导出用户数据到Excel
     */
    public void exportUsers(HttpServletResponse response) throws IOException {
        List<User> users = userMapper.selectListByQuery(
            QueryWrapper.create().where(USER.IS_DELETED.eq(0))
        );
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("用户数据", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        
        // 创建Excel写入器
        ExcelWriter writer = ExcelUtil.getWriter(true);
        
        // 设置表头
        writer.addHeaderAlias("id", "用户ID");
        writer.addHeaderAlias("name", "用户名");
        writer.addHeaderAlias("age", "年龄");
        writer.addHeaderAlias("deptId", "部门ID");
        writer.addHeaderAlias("status", "状态");
        writer.addHeaderAlias("createTime", "创建时间");
        
        // 写入数据
        writer.write(users, true);
        
        // 输出到响应流
        writer.flush(response.getOutputStream());
        writer.close();
    }

    /**
     * 从Excel导入用户数据
     */
    public List<User> importUsers(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        
        // 设置表头别名
        reader.addHeaderAlias("用户名", "name");
        reader.addHeaderAlias("年龄", "age");
        reader.addHeaderAlias("部门ID", "deptId");
        reader.addHeaderAlias("状态", "status");
        
        // 读取数据
        List<UserDTO> userDTOs = reader.readAll(UserDTO.class);
        List<User> users = new ArrayList<>();
        
        for (UserDTO userDTO : userDTOs) {
            User user = new User();
            BeanUtil.copyProperties(userDTO, user);
            userMapper.insert(user);
            users.add(user);
        }
        
        reader.close();
        return users;
    }
}
