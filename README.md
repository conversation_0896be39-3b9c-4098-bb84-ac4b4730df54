# MyBatis-Flex CRUD Demo

基于 MyBatis-Flex 框架的完整 CRUD 示例项目，提供多种参数形式的接口，包括表单提交、JSON提交、文件传输等。

## 技术栈

- **Spring Boot 2.7.18** - Web框架
- **MyBatis-Flex 1.8.9** - 数据库操作框架
- **HuTool 5.8.25** - Java工具类库
- **Apache POI 5.2.4** - Excel文件处理
- **MySQL 8.0** - 数据库

## 项目特性

### 1. 多种参数形式支持
- **GET请求参数**: 适用于简单查询
- **POST JSON**: 适用于复杂数据提交
- **表单提交**: 适用于传统表单场景
- **文件上传**: 支持Excel文件导入导出

### 2. 完整的CRUD操作
- 分页查询
- 条件搜索
- 新增数据
- 更新数据
- 删除数据（逻辑删除）
- 批量操作

### 3. 导入导出功能
- Excel文件导入
- Excel文件导出
- 数据模板下载

## 数据库配置

### 数据库连接信息
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: root
```

### 数据库表结构
项目包含三个主要表：
- `tb_user` - 用户表
- `tb_department` - 部门表  
- `tb_account` - 账户表

## 快速开始

### 1. 环境准备
- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 2. 数据库初始化
执行以下SQL脚本创建数据库和表：

```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS flex_demo CHARACTER SET utf8 COLLATE utf8_general_ci;

USE flex_demo;

-- 创建用户表
CREATE TABLE tb_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    name VARCHAR(50) NOT NULL COMMENT '用户名',
    age INT DEFAULT NULL COMMENT '年龄',
    dept_id BIGINT DEFAULT NULL COMMENT '部门ID',
    account_id BIGINT DEFAULT NULL COMMENT '账户ID',
    status TINYINT DEFAULT 0 COMMENT '状态：0-正常，1-禁用',
    is_deleted TINYINT DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';

-- 创建部门表
CREATE TABLE tb_department (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '部门ID',
    name VARCHAR(50) NOT NULL COMMENT '部门名称',
    parent_id BIGINT DEFAULT NULL COMMENT '上级部门ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='部门表';

-- 创建账户表
CREATE TABLE tb_account (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '账户余额',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='账户表';

-- 插入测试数据
INSERT INTO tb_department (id, name) VALUES 
(1, '技术部'), (2, '市场部'), (3, '人事部');

INSERT INTO tb_user (name, age, dept_id) VALUES 
('张三', 25, 1), ('李四', 30, 2), ('王五', 28, 1), ('赵六', 32, 3);

INSERT INTO tb_account (user_id, balance) VALUES 
(1, 1000.00), (2, 2000.00), (3, 1500.00), (4, 3000.00);
```

### 3. 启动项目
```bash
mvn spring-boot:run
```

### 4. 访问测试页面
打开浏览器访问：http://localhost:8080

## API接口文档

### 用户管理接口

#### 1. 分页查询用户 (GET)
```
GET /api/users?page=1&size=10&keyword=张
```

#### 2. 搜索用户 (POST JSON)
```
POST /api/users/search
Content-Type: application/json

{
  "page": 1,
  "size": 10,
  "keyword": "张"
}
```

#### 3. 创建用户 (表单)
```
POST /api/users/form
Content-Type: application/x-www-form-urlencoded

name=张三&age=25&deptId=1&status=0
```

#### 4. 创建用户 (JSON)
```
POST /api/users
Content-Type: application/json

{
  "name": "张三",
  "age": 25,
  "deptId": 1,
  "status": 0
}
```

#### 5. 更新用户
```
PUT /api/users/{id}
PUT /api/users/{id}/form
```

#### 6. 删除用户
```
DELETE /api/users/{id}
DELETE /api/users/batch (批量删除)
```

#### 7. 导入导出
```
POST /api/users/import (文件上传)
GET /api/users/export (文件下载)
```

### 部门管理接口

#### 1. 查询所有部门
```
GET /api/departments/all
```

#### 2. 分页查询部门
```
GET /api/departments?page=1&size=10&keyword=技术
POST /api/departments/search
```

#### 3. 部门CRUD操作
```
POST /api/departments
POST /api/departments/form
PUT /api/departments/{id}
PUT /api/departments/{id}/form
DELETE /api/departments/{id}
```

## 项目结构

```
src/
├── main/
│   ├── java/com/example/
│   │   ├── FlexDemoApplication.java     # 启动类
│   │   ├── entity/                      # 实体类
│   │   │   ├── User.java
│   │   │   ├── Department.java
│   │   │   └── Account.java
│   │   ├── dto/                         # 数据传输对象
│   │   │   └── UserDTO.java
│   │   ├── mapper/                      # Mapper接口
│   │   │   ├── UserMapper.java
│   │   │   ├── DepartmentMapper.java
│   │   │   └── AccountMapper.java
│   │   ├── service/                     # 服务层
│   │   │   ├── UserService.java
│   │   │   └── DepartmentService.java
│   │   ├── controller/                  # 控制器层
│   │   │   ├── UserController.java
│   │   │   └── DepartmentController.java
│   │   ├── common/                      # 通用类
│   │   │   ├── Result.java
│   │   │   └── PageQuery.java
│   │   └── config/                      # 配置类
│   │       └── GlobalExceptionHandler.java
│   └── resources/
│       ├── application.yml              # 配置文件
│       └── static/
│           └── index.html               # 测试页面
└── pom.xml                              # Maven配置
```

## 测试说明

项目提供了完整的测试页面 (http://localhost:8080)，包含以下测试功能：

1. **查询测试** - GET请求参数形式
2. **搜索测试** - POST JSON形式
3. **创建测试** - 表单提交和JSON提交
4. **文件测试** - Excel导入导出
5. **部门管理** - 基础CRUD操作

每个测试都会显示请求的API地址和返回结果，方便调试和学习。

## 项目启动

### 方式一：使用Maven启动
```bash
mvn spring-boot:run
```

### 方式二：打包后启动
```bash
mvn clean package -DskipTests
java -jar target/mybatis-flex-demo-1.0.0.jar
```

## 测试验证

### 1. 访问测试页面
启动应用后，访问：http://localhost:8080

### 2. 运行API测试脚本
```bash
powershell -ExecutionPolicy Bypass -File api-test.ps1
```

### 3. 手动测试API
```bash
# 查询所有部门
curl -X GET "http://localhost:8080/api/departments/all"

# 分页查询用户
curl -X GET "http://localhost:8080/api/users?page=1&size=10"

# 创建用户 (JSON)
curl -X POST "http://localhost:8080/api/users" \
  -H "Content-Type: application/json" \
  -d '{"name":"张三","age":25,"deptId":1,"status":0}'

# 创建部门 (表单)
curl -X POST "http://localhost:8080/api/departments/form" \
  -d "name=新部门&parentId=1"
```

## 功能特性验证

✅ **多种参数形式支持**
- GET请求参数：`/api/users?page=1&size=10`
- POST JSON：`/api/users/search`
- 表单提交：`/api/users/form`
- 文件上传：`/api/users/import`
- 路径参数：`/api/users/{id}`

✅ **完整的CRUD操作**
- 创建：POST `/api/users`
- 查询：GET `/api/users`
- 更新：PUT `/api/users/{id}`
- 删除：DELETE `/api/users/{id}`

✅ **导入导出功能**
- Excel导入：POST `/api/users/import`
- Excel导出：GET `/api/users/export`

✅ **数据验证和异常处理**
- 参数校验
- 全局异常处理
- 统一响应格式

## 注意事项

1. 确保MySQL服务已启动并创建了相应的数据库
2. 修改 `application.yml` 中的数据库连接信息
3. 项目使用了逻辑删除，删除操作不会真正删除数据
4. Excel导入功能支持 `.xlsx` 和 `.xls` 格式
5. 所有接口都有统一的异常处理和参数校验

## Git版本控制

### 快速初始化Git仓库

```bash
# 使用提供的脚本快速初始化
powershell -ExecutionPolicy Bypass -File git-init.ps1

# 或手动初始化
git init
git add .
git commit -m "Initial commit"
```

### Git忽略文件

项目已配置完整的 `.gitignore` 文件，包含：
- Java编译文件 (`*.class`, `target/`)
- IDE配置文件 (`.idea/`, `*.iml`, `.vscode/`)
- 日志文件 (`*.log`)
- 操作系统文件 (`.DS_Store`, `Thumbs.db`)
- 敏感配置文件 (`application-local.yml`)
- 临时文件和缓存

### 详细Git使用指南

请参考 [GIT_GUIDE.md](GIT_GUIDE.md) 获取完整的Git使用说明。

## 项目状态

🎉 **项目已成功搭建并测试通过！**

- ✅ 编译成功
- ✅ 启动成功 (端口: 8080)
- ✅ 数据库连接正常
- ✅ 所有API接口测试通过
- ✅ 测试页面可正常访问
- ✅ Git版本控制配置完成
