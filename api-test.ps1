# MyBatis-Flex CRUD API Test Script

$baseUrl = "http://localhost:8080"

Write-Host "=== MyBatis-Flex CRUD API Test ===" -ForegroundColor Green

# 1. Test get all departments
Write-Host "`n1. Testing get all departments" -ForegroundColor Yellow
$response = Invoke-WebRequest -Uri "$baseUrl/api/departments/all" -Method GET
Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green

# 2. Test paginated user query
Write-Host "`n2. Testing paginated user query" -ForegroundColor Yellow
$uri = "$baseUrl/api/users?page=1&size=5"
$response = Invoke-WebRequest -Uri $uri -Method GET
Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green

# 3. Test create user (JSON)
Write-Host "`n3. Testing create user (JSON)" -ForegroundColor Yellow
$body = '{"name":"TestUser123","age":25,"deptId":1,"status":0}'
$response = Invoke-WebRequest -Uri "$baseUrl/api/users" -Method POST -Body $body -ContentType "application/json"
Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green

# 4. Test create department (Form)
Write-Host "`n4. Testing create department (Form)" -ForegroundColor Yellow
$formData = "name=TestDept123&parentId=1"
$response = Invoke-WebRequest -Uri "$baseUrl/api/departments/form" -Method POST -Body $formData -ContentType "application/x-www-form-urlencoded"
Write-Host "Status Code: $($response.StatusCode)" -ForegroundColor Green

Write-Host "`n=== Test Completed ===" -ForegroundColor Green
Write-Host "All API tests passed! Visit http://localhost:8080 for the full test page" -ForegroundColor Cyan
