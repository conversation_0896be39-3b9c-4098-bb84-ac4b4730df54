# Git 使用指南

本文档提供了项目的Git版本控制使用指南。

## 快速开始

### 1. 初始化Git仓库（首次使用）

```bash
# 方式一：使用提供的脚本
powershell -ExecutionPolicy Bypass -File git-init.ps1

# 方式二：手动初始化
git init
git add .
git commit -m "Initial commit"
```

### 2. 连接远程仓库

```bash
# 添加远程仓库
git remote add origin <your-repository-url>

# 推送到远程仓库
git push -u origin main
```

## Git忽略文件说明

项目已配置了完整的 `.gitignore` 文件，包含以下忽略规则：

### Java相关
- `*.class` - 编译后的字节码文件
- `*.jar`, `*.war` - 打包文件
- `target/` - Maven构建目录
- `*.log` - 日志文件

### IDE相关
- `.idea/` - IntelliJ IDEA配置
- `*.iml`, `*.ipr`, `*.iws` - IDEA项目文件
- `.vscode/` - VS Code配置
- `.settings/`, `.project`, `.classpath` - Eclipse配置

### 操作系统相关
- `.DS_Store` - macOS系统文件
- `Thumbs.db` - Windows缩略图
- `*~` - Linux临时文件

### 应用相关
- `application-local.yml` - 本地配置文件
- `uploads/`, `downloads/` - 上传下载目录
- `*.backup`, `*.bak` - 备份文件

## 常用Git命令

### 基本操作

```bash
# 查看状态
git status

# 查看修改内容
git diff

# 添加文件到暂存区
git add .                    # 添加所有文件
git add <filename>           # 添加指定文件

# 提交更改
git commit -m "提交信息"

# 查看提交历史
git log --oneline           # 简洁格式
git log --graph --oneline   # 图形化显示
```

### 分支操作

```bash
# 查看分支
git branch                  # 查看本地分支
git branch -r               # 查看远程分支
git branch -a               # 查看所有分支

# 创建分支
git checkout -b feature/new-feature    # 创建并切换到新分支
git branch feature/new-feature         # 仅创建分支

# 切换分支
git checkout main           # 切换到main分支
git checkout develop        # 切换到develop分支

# 合并分支
git checkout main           # 切换到目标分支
git merge feature/new-feature  # 合并指定分支

# 删除分支
git branch -d feature/new-feature      # 删除本地分支
git push origin --delete feature/new-feature  # 删除远程分支
```

### 远程操作

```bash
# 查看远程仓库
git remote -v

# 拉取更新
git pull origin main        # 拉取并合并
git fetch origin            # 仅拉取不合并

# 推送更改
git push origin main        # 推送到main分支
git push origin feature/new-feature  # 推送到指定分支

# 推送标签
git tag v1.0.0              # 创建标签
git push origin v1.0.0      # 推送标签
git push origin --tags      # 推送所有标签
```

## 推荐的工作流程

### 1. Feature分支工作流

```bash
# 1. 从main分支创建feature分支
git checkout main
git pull origin main
git checkout -b feature/user-management

# 2. 在feature分支上开发
# ... 进行开发工作 ...
git add .
git commit -m "Add user management functionality"

# 3. 推送feature分支
git push origin feature/user-management

# 4. 创建Pull Request/Merge Request
# 在GitHub/GitLab等平台上创建PR/MR

# 5. 合并后清理
git checkout main
git pull origin main
git branch -d feature/user-management
```

### 2. 提交信息规范

使用以下格式编写提交信息：

```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型 (type):**
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构代码
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例:**
```bash
git commit -m "feat(user): add user registration API

- Add user registration endpoint
- Implement email validation
- Add password encryption

Closes #123"
```

## 项目特定的Git配置

### 1. 文件属性配置

项目包含 `.gitattributes` 文件，确保：
- Java文件使用LF换行符
- Windows脚本使用CRLF换行符
- 二进制文件正确处理

### 2. 忽略敏感配置

以下配置文件被忽略，需要手动创建：
- `application-local.yml` - 本地开发配置
- `application-dev.yml` - 开发环境配置
- `application-prod.yml` - 生产环境配置

### 3. 数据库相关

- 数据库备份文件 (`*.sql.backup`) 被忽略
- 本地数据库文件 (`*.db`) 被忽略
- 建议将数据库初始化脚本放在 `src/main/resources/db/` 目录

## 故障排除

### 1. 文件权限问题

```bash
# Windows上的换行符问题
git config core.autocrlf true

# 忽略文件权限变化
git config core.filemode false
```

### 2. 大文件处理

```bash
# 如果需要跟踪大文件，使用Git LFS
git lfs track "*.xlsx"
git lfs track "*.jar"
git add .gitattributes
```

### 3. 撤销操作

```bash
# 撤销工作区修改
git checkout -- <filename>

# 撤销暂存区修改
git reset HEAD <filename>

# 撤销最后一次提交
git reset --soft HEAD~1    # 保留修改
git reset --hard HEAD~1    # 丢弃修改
```

## 最佳实践

1. **频繁提交**: 小步快跑，频繁提交代码
2. **清晰的提交信息**: 使用规范的提交信息格式
3. **分支管理**: 使用feature分支进行开发
4. **代码审查**: 通过Pull Request进行代码审查
5. **标签管理**: 为重要版本打标签
6. **备份**: 定期推送到远程仓库

## 相关资源

- [Git官方文档](https://git-scm.com/doc)
- [GitHub Flow](https://guides.github.com/introduction/flow/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Git LFS](https://git-lfs.github.io/)
