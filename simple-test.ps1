# 简单的API测试脚本

$baseUrl = "http://localhost:8080"

Write-Host "=== MyBatis-Flex CRUD API 测试 ===" -ForegroundColor Green

# 1. 测试查询所有部门
Write-Host "`n1. 测试查询所有部门" -ForegroundColor Yellow
$response = Invoke-WebRequest -Uri "$baseUrl/api/departments/all" -Method GET
Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green

# 2. 测试分页查询用户
Write-Host "`n2. 测试分页查询用户" -ForegroundColor Yellow
$uri = "$baseUrl/api/users?page=1&size=5"
$response = Invoke-WebRequest -Uri $uri -Method GET
Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green

# 3. 测试创建用户 (JSON)
Write-Host "`n3. 测试创建用户 (JSON)" -ForegroundColor Yellow
$body = '{"name":"测试用户123","age":25,"deptId":1,"status":0}'
$response = Invoke-WebRequest -Uri "$baseUrl/api/users" -Method POST -Body $body -ContentType "application/json"
Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green

# 4. 测试创建部门 (表单)
Write-Host "`n4. 测试创建部门 (表单)" -ForegroundColor Yellow
$formData = "name=测试部门123&parentId=1"
$response = Invoke-WebRequest -Uri "$baseUrl/api/departments/form" -Method POST -Body $formData -ContentType "application/x-www-form-urlencoded"
Write-Host "状态码: $($response.StatusCode)" -ForegroundColor Green

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "所有API测试通过！请访问 http://localhost:8080 查看完整的测试页面" -ForegroundColor Cyan
