# Git 初始化脚本
# 用于快速初始化Git仓库并进行首次提交

Write-Host "=== Git Repository Initialization ===" -ForegroundColor Green

# 检查是否已经是Git仓库
if (Test-Path ".git") {
    Write-Host "Git repository already exists!" -ForegroundColor Yellow
    $continue = Read-Host "Do you want to continue? (y/n)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "Operation cancelled." -ForegroundColor Red
        exit
    }
} else {
    # 初始化Git仓库
    Write-Host "`nInitializing Git repository..." -ForegroundColor Yellow
    git init
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Git repository initialized successfully!" -ForegroundColor Green
    } else {
        Write-Host "Failed to initialize Git repository!" -ForegroundColor Red
        exit 1
    }
}

# 添加所有文件到暂存区
Write-Host "`nAdding files to staging area..." -ForegroundColor Yellow
git add .

if ($LASTEXITCODE -eq 0) {
    Write-Host "Files added to staging area successfully!" -ForegroundColor Green
} else {
    Write-Host "Failed to add files to staging area!" -ForegroundColor Red
    exit 1
}

# 显示状态
Write-Host "`nGit status:" -ForegroundColor Yellow
git status --short

# 进行首次提交
Write-Host "`nMaking initial commit..." -ForegroundColor Yellow
git commit -m "Initial commit: MyBatis-Flex CRUD demo project

Features:
- Complete CRUD operations for User, Department, Account entities
- Multiple parameter forms support (GET params, POST JSON, Form data, File upload)
- Import/Export functionality with Excel files
- Data validation and global exception handling
- HuTool utilities integration
- MyBatis-Flex ORM framework
- Spring Boot 2.7.18
- Comprehensive test page and API test scripts

Tech Stack:
- Spring Boot 2.7.18
- MyBatis-Flex 1.8.9
- HuTool 5.8.25
- Apache POI 5.2.4
- MySQL 8.0
- HikariCP"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Initial commit created successfully!" -ForegroundColor Green
} else {
    Write-Host "Failed to create initial commit!" -ForegroundColor Red
    exit 1
}

# 显示提交历史
Write-Host "`nCommit history:" -ForegroundColor Yellow
git log --oneline -5

Write-Host "`n=== Git Repository Setup Complete ===" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Add remote repository: git remote add origin <repository-url>" -ForegroundColor White
Write-Host "2. Push to remote: git push -u origin main" -ForegroundColor White
Write-Host "3. Create development branch: git checkout -b develop" -ForegroundColor White

Write-Host "`nUseful Git commands:" -ForegroundColor Cyan
Write-Host "- Check status: git status" -ForegroundColor White
Write-Host "- View changes: git diff" -ForegroundColor White
Write-Host "- Add files: git add ." -ForegroundColor White
Write-Host "- Commit changes: git commit -m 'message'" -ForegroundColor White
Write-Host "- View history: git log --oneline" -ForegroundColor White
