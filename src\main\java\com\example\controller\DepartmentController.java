package com.example.controller;

import com.example.common.PageQuery;
import com.example.common.Result;
import com.example.entity.Department;
import com.example.service.DepartmentService;
import com.mybatisflex.core.paginate.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门控制器
 */
@RestController
@RequestMapping("/api/departments")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 分页查询部门 - GET请求参数形式
     */
    @GetMapping
    public Result<Page<Department>> getDepartments(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        
        Page<Department> departmentPage = departmentService.pageDepartments(page, size, keyword);
        return Result.success(departmentPage);
    }

    /**
     * 查询所有部门
     */
    @GetMapping("/all")
    public Result<List<Department>> getAllDepartments() {
        List<Department> departments = departmentService.getAllDepartments();
        return Result.success(departments);
    }

    /**
     * 分页查询部门 - POST请求JSON形式
     */
    @PostMapping("/search")
    public Result<Page<Department>> searchDepartments(@RequestBody PageQuery pageQuery) {
        Page<Department> departmentPage = departmentService.pageDepartments(
            pageQuery.getPage(), 
            pageQuery.getSize(), 
            pageQuery.getKeyword()
        );
        return Result.success(departmentPage);
    }

    /**
     * 根据ID查询部门
     */
    @GetMapping("/{id}")
    public Result<Department> getDepartmentById(@PathVariable Long id) {
        Department department = departmentService.getDepartmentById(id);
        if (department == null) {
            return Result.error("部门不存在");
        }
        return Result.success(department);
    }

    /**
     * 创建部门 - JSON请求体形式
     */
    @PostMapping
    public Result<Department> createDepartment(@RequestBody Department department) {
        Department createdDepartment = departmentService.createDepartment(department);
        return Result.success("部门创建成功", createdDepartment);
    }

    /**
     * 创建部门 - 表单提交形式
     */
    @PostMapping("/form")
    public Result<Department> createDepartmentByForm(
            @RequestParam String name,
            @RequestParam(required = false) Long parentId) {
        
        Department department = new Department();
        department.setName(name);
        department.setParentId(parentId);
        
        Department createdDepartment = departmentService.createDepartment(department);
        return Result.success("部门创建成功", createdDepartment);
    }

    /**
     * 更新部门 - JSON请求体形式
     */
    @PutMapping("/{id}")
    public Result<Department> updateDepartment(@PathVariable Long id, @RequestBody Department department) {
        Department updatedDepartment = departmentService.updateDepartment(id, department);
        return Result.success("部门更新成功", updatedDepartment);
    }

    /**
     * 更新部门 - 表单提交形式
     */
    @PutMapping("/{id}/form")
    public Result<Department> updateDepartmentByForm(
            @PathVariable Long id,
            @RequestParam String name,
            @RequestParam(required = false) Long parentId) {
        
        Department department = new Department();
        department.setName(name);
        department.setParentId(parentId);
        
        Department updatedDepartment = departmentService.updateDepartment(id, department);
        return Result.success("部门更新成功", updatedDepartment);
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteDepartment(@PathVariable Long id) {
        departmentService.deleteDepartment(id);
        return Result.success("部门删除成功");
    }
}
