# Auto detect text files and perform LF normalization
* text=auto

# Java files
*.java text eol=lf
*.class binary
*.jar binary

# XML files
*.xml text eol=lf
*.xsd text eol=lf
*.xsl text eol=lf

# Properties files
*.properties text eol=lf

# YAML files
*.yml text eol=lf
*.yaml text eol=lf

# JSON files
*.json text eol=lf

# SQL files
*.sql text eol=lf

# Shell scripts
*.sh text eol=lf
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# Web files
*.html text eol=lf
*.css text eol=lf
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf

# Markdown files
*.md text eol=lf
*.markdown text eol=lf

# Configuration files
*.conf text eol=lf
*.config text eol=lf
*.ini text eol=lf
*.cfg text eol=lf

# Documentation
*.txt text eol=lf
*.rtf text eol=lf

# Images
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text eol=lf

# Archives
*.zip binary
*.tar binary
*.gz binary
*.rar binary
*.7z binary

# Office documents
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary
*.pdf binary

# Fonts
*.ttf binary
*.otf binary
*.woff binary
*.woff2 binary
*.eot binary

# Audio/Video
*.mp3 binary
*.mp4 binary
*.avi binary
*.mov binary
*.wav binary

# Maven wrapper
mvnw text eol=lf
mvnw.cmd text eol=crlf

# Gradle wrapper
gradlew text eol=lf
gradlew.bat text eol=crlf

# License files
LICENSE text eol=lf
COPYING text eol=lf

# Ignore files
.gitignore text eol=lf
.gitattributes text eol=lf

# IDE files
*.iml text eol=lf
*.ipr text eol=lf
*.iws text eol=lf

# Log files
*.log text eol=lf
