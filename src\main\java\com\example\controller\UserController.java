package com.example.controller;

import cn.hutool.core.collection.CollUtil;
import com.example.common.PageQuery;
import com.example.common.Result;
import com.example.dto.UserDTO;
import com.example.entity.User;
import com.example.service.UserService;
import com.mybatisflex.core.paginate.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 用户控制器
 * 提供多种参数形式的接口：表单提交、JSON提交、文件传输等
 */
@RestController
@RequestMapping("/api/users")
@Validated
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 分页查询用户 - GET请求参数形式
     */
    @GetMapping
    public Result<Page<User>> getUsers(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        
        Page<User> userPage = userService.pageUsers(page, size, keyword);
        return Result.success(userPage);
    }

    /**
     * 分页查询用户 - POST请求JSON形式
     */
    @PostMapping("/search")
    public Result<Page<User>> searchUsers(@RequestBody PageQuery pageQuery) {
        Page<User> userPage = userService.pageUsers(
            pageQuery.getPage(), 
            pageQuery.getSize(), 
            pageQuery.getKeyword()
        );
        return Result.success(userPage);
    }

    /**
     * 根据ID查询用户 - 路径参数形式
     */
    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Long id) {
        User user = userService.getUserById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }
        return Result.success(user);
    }

    /**
     * 创建用户 - JSON请求体形式
     */
    @PostMapping
    public Result<User> createUser(@Valid @RequestBody UserDTO userDTO) {
        User user = userService.createUser(userDTO);
        return Result.success("用户创建成功", user);
    }

    /**
     * 创建用户 - 表单提交形式
     */
    @PostMapping("/form")
    public Result<User> createUserByForm(
            @RequestParam String name,
            @RequestParam Integer age,
            @RequestParam(required = false) Long deptId,
            @RequestParam(defaultValue = "0") Integer status) {
        
        UserDTO userDTO = new UserDTO();
        userDTO.setName(name);
        userDTO.setAge(age);
        userDTO.setDeptId(deptId);
        userDTO.setStatus(status);
        
        User user = userService.createUser(userDTO);
        return Result.success("用户创建成功", user);
    }

    /**
     * 更新用户 - JSON请求体形式
     */
    @PutMapping("/{id}")
    public Result<User> updateUser(@PathVariable Long id, @Valid @RequestBody UserDTO userDTO) {
        User user = userService.updateUser(id, userDTO);
        return Result.success("用户更新成功", user);
    }

    /**
     * 更新用户 - 表单提交形式
     */
    @PutMapping("/{id}/form")
    public Result<User> updateUserByForm(
            @PathVariable Long id,
            @RequestParam String name,
            @RequestParam Integer age,
            @RequestParam(required = false) Long deptId,
            @RequestParam(defaultValue = "0") Integer status) {
        
        UserDTO userDTO = new UserDTO();
        userDTO.setName(name);
        userDTO.setAge(age);
        userDTO.setDeptId(deptId);
        userDTO.setStatus(status);
        
        User user = userService.updateUser(id, userDTO);
        return Result.success("用户更新成功", user);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return Result.success("用户删除成功");
    }

    /**
     * 批量删除用户 - JSON数组形式
     */
    @DeleteMapping("/batch")
    public Result<String> batchDeleteUsers(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Result.error("请选择要删除的用户");
        }
        userService.batchDeleteUsers(ids);
        return Result.success("批量删除成功");
    }

    /**
     * 批量删除用户 - 表单参数形式
     */
    @DeleteMapping("/batch/form")
    public Result<String> batchDeleteUsersByForm(@RequestParam("ids") List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Result.error("请选择要删除的用户");
        }
        userService.batchDeleteUsers(ids);
        return Result.success("批量删除成功");
    }

    /**
     * 导出用户数据到Excel
     */
    @GetMapping("/export")
    public void exportUsers(HttpServletResponse response) throws IOException {
        userService.exportUsers(response);
    }

    /**
     * 从Excel导入用户数据 - 文件上传形式
     */
    @PostMapping("/import")
    public Result<List<User>> importUsers(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择要导入的文件");
        }
        
        try {
            List<User> users = userService.importUsers(file);
            return Result.success("导入成功，共导入 " + users.size() + " 条数据", users);
        } catch (IOException e) {
            return Result.error("文件读取失败：" + e.getMessage());
        } catch (Exception e) {
            return Result.error("导入失败：" + e.getMessage());
        }
    }
}
