# Git状态检查脚本

Write-Host "=== Git Configuration Check ===" -ForegroundColor Green

# 检查Git是否安装
Write-Host "`nChecking Git installation..." -ForegroundColor Yellow
try {
    $gitVersion = git --version
    Write-Host "✅ Git is installed: $gitVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Git is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Git from: https://git-scm.com/download/windows" -ForegroundColor Cyan
    exit 1
}

# 检查是否已初始化Git仓库
Write-Host "`nChecking Git repository status..." -ForegroundColor Yellow
if (Test-Path ".git") {
    Write-Host "✅ Git repository exists" -ForegroundColor Green
    
    # 显示Git状态
    Write-Host "`nGit status:" -ForegroundColor Yellow
    git status --short
    
    # 显示分支信息
    Write-Host "`nCurrent branch:" -ForegroundColor Yellow
    git branch --show-current
    
    # 显示远程仓库
    Write-Host "`nRemote repositories:" -ForegroundColor Yellow
    $remotes = git remote -v
    if ($remotes) {
        $remotes
    } else {
        Write-Host "No remote repositories configured" -ForegroundColor Gray
    }
    
    # 显示最近的提交
    Write-Host "`nRecent commits:" -ForegroundColor Yellow
    git log --oneline -5
    
} else {
    Write-Host "❌ Not a Git repository" -ForegroundColor Red
    Write-Host "Run 'powershell -ExecutionPolicy Bypass -File git-init.ps1' to initialize" -ForegroundColor Cyan
}

# 检查Git配置文件
Write-Host "`nChecking Git ignore files..." -ForegroundColor Yellow
if (Test-Path ".gitignore") {
    Write-Host "✅ .gitignore file exists" -ForegroundColor Green
} else {
    Write-Host "❌ .gitignore file missing" -ForegroundColor Red
}

if (Test-Path ".gitattributes") {
    Write-Host "✅ .gitattributes file exists" -ForegroundColor Green
} else {
    Write-Host "❌ .gitattributes file missing" -ForegroundColor Red
}

# 检查Git用户配置
Write-Host "`nChecking Git user configuration..." -ForegroundColor Yellow
$userName = git config user.name
$userEmail = git config user.email

if ($userName) {
    Write-Host "✅ Git user.name: $userName" -ForegroundColor Green
} else {
    Write-Host "❌ Git user.name not configured" -ForegroundColor Red
    Write-Host "Set with: git config --global user.name 'Your Name'" -ForegroundColor Cyan
}

if ($userEmail) {
    Write-Host "✅ Git user.email: $userEmail" -ForegroundColor Green
} else {
    Write-Host "❌ Git user.email not configured" -ForegroundColor Red
    Write-Host "Set with: git config --global user.email '<EMAIL>'" -ForegroundColor Cyan
}

Write-Host "`n=== Check Complete ===" -ForegroundColor Green
