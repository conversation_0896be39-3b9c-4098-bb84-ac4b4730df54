package com.example.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.example.entity.Department;
import com.example.mapper.DepartmentMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.example.entity.table.DepartmentTableDef.DEPARTMENT;

/**
 * 部门服务类
 */
@Service
public class DepartmentService {

    @Autowired
    private DepartmentMapper departmentMapper;

    /**
     * 分页查询部门
     */
    public Page<Department> pageDepartments(int page, int size, String keyword) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        
        if (StrUtil.isNotBlank(keyword)) {
            queryWrapper.where(DEPARTMENT.NAME.like(keyword));
        }
        
        return departmentMapper.paginate(page, size, queryWrapper);
    }

    /**
     * 查询所有部门
     */
    public List<Department> getAllDepartments() {
        return departmentMapper.selectAll();
    }

    /**
     * 根据ID查询部门
     */
    public Department getDepartmentById(Long id) {
        return departmentMapper.selectOneById(id);
    }

    /**
     * 创建部门
     */
    public Department createDepartment(Department department) {
        departmentMapper.insert(department);
        return department;
    }

    /**
     * 更新部门
     */
    public Department updateDepartment(Long id, Department departmentData) {
        Department department = departmentMapper.selectOneById(id);
        if (department == null) {
            throw new RuntimeException("部门不存在");
        }
        
        BeanUtil.copyProperties(departmentData, department, "id");
        departmentMapper.update(department);
        return department;
    }

    /**
     * 删除部门
     */
    public void deleteDepartment(Long id) {
        Department department = departmentMapper.selectOneById(id);
        if (department == null) {
            throw new RuntimeException("部门不存在");
        }
        
        departmentMapper.deleteById(id);
    }
}
