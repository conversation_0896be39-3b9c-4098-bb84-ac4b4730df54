# 项目文件清单

本文档列出了MyBatis-Flex CRUD Demo项目的所有文件及其用途。

## 📁 项目结构

```
mybatis-flex-demo/
├── 📄 .gitignore                    # Git忽略文件配置
├── 📄 .gitattributes               # Git文件属性配置
├── 📄 pom.xml                      # Maven项目配置文件
├── 📄 README.md                    # 项目说明文档
├── 📄 GIT_GUIDE.md                 # Git使用指南
├── 📄 PROJECT_FILES.md             # 项目文件清单（本文件）
├── 📄 git-init.ps1                 # Git初始化脚本
├── 📄 check-git.ps1                # Git状态检查脚本
├── 📄 api-test.ps1                 # API测试脚本
├── 📄 simple-test.ps1              # 简单测试脚本
├── 📄 test-api.ps1                 # 测试API脚本
├── 📁 src/
│   └── 📁 main/
│       ├── 📁 java/com/example/
│       │   ├── 📄 FlexDemoApplication.java      # Spring Boot启动类
│       │   ├── 📁 entity/                       # 实体类
│       │   │   ├── 📄 User.java                 # 用户实体
│       │   │   ├── 📄 Account.java              # 账户实体
│       │   │   └── 📄 Department.java           # 部门实体
│       │   ├── 📁 dto/                          # 数据传输对象
│       │   │   └── 📄 UserDTO.java              # 用户DTO
│       │   ├── 📁 mapper/                       # Mapper接口
│       │   │   ├── 📄 UserMapper.java           # 用户Mapper
│       │   │   ├── 📄 AccountMapper.java        # 账户Mapper
│       │   │   └── 📄 DepartmentMapper.java     # 部门Mapper
│       │   ├── 📁 service/                      # 服务层
│       │   │   ├── 📄 UserService.java          # 用户服务
│       │   │   └── 📄 DepartmentService.java    # 部门服务
│       │   ├── 📁 controller/                   # 控制器层
│       │   │   ├── 📄 UserController.java       # 用户控制器
│       │   │   └── 📄 DepartmentController.java # 部门控制器
│       │   ├── 📁 common/                       # 通用类
│       │   │   ├── 📄 Result.java               # 统一响应结果
│       │   │   └── 📄 PageQuery.java            # 分页查询参数
│       │   └── 📁 config/                       # 配置类
│       │       ├── 📄 GlobalExceptionHandler.java # 全局异常处理
│       │       └── 📄 MyBatisFlexConfig.java    # MyBatis-Flex配置
│       └── 📁 resources/
│           ├── 📄 application.yml               # 应用配置文件
│           └── 📁 static/
│               └── 📄 index.html                # 测试页面
└── 📁 target/                                   # Maven构建目录（被Git忽略）
```

## 📋 文件详细说明

### 🔧 配置文件

| 文件 | 用途 | 说明 |
|------|------|------|
| `pom.xml` | Maven项目配置 | 定义依赖、插件、构建配置 |
| `application.yml` | Spring Boot配置 | 数据库连接、MyBatis-Flex配置 |
| `.gitignore` | Git忽略规则 | 定义不需要版本控制的文件 |
| `.gitattributes` | Git文件属性 | 定义文件的换行符、编码等属性 |

### 📖 文档文件

| 文件 | 用途 | 说明 |
|------|------|------|
| `README.md` | 项目主文档 | 项目介绍、使用说明、API文档 |
| `GIT_GUIDE.md` | Git使用指南 | Git命令、工作流程、最佳实践 |
| `PROJECT_FILES.md` | 文件清单 | 项目文件结构和说明（本文件） |

### 🛠 脚本文件

| 文件 | 用途 | 说明 |
|------|------|------|
| `git-init.ps1` | Git初始化 | 快速初始化Git仓库并首次提交 |
| `check-git.ps1` | Git状态检查 | 检查Git配置和仓库状态 |
| `api-test.ps1` | API测试 | 测试所有API接口功能 |
| `simple-test.ps1` | 简单测试 | 基础API测试脚本 |
| `test-api.ps1` | 测试脚本 | 完整的API功能测试 |

### ☕ Java源码文件

#### 实体类 (Entity)
- `User.java` - 用户实体，包含用户基本信息
- `Account.java` - 账户实体，包含账户余额信息
- `Department.java` - 部门实体，包含部门信息

#### 数据传输对象 (DTO)
- `UserDTO.java` - 用户数据传输对象，用于API参数传递

#### 数据访问层 (Mapper)
- `UserMapper.java` - 用户数据访问接口
- `AccountMapper.java` - 账户数据访问接口
- `DepartmentMapper.java` - 部门数据访问接口

#### 业务逻辑层 (Service)
- `UserService.java` - 用户业务逻辑，包含CRUD和导入导出
- `DepartmentService.java` - 部门业务逻辑

#### 控制器层 (Controller)
- `UserController.java` - 用户API接口，支持多种参数形式
- `DepartmentController.java` - 部门API接口

#### 通用类 (Common)
- `Result.java` - 统一API响应格式
- `PageQuery.java` - 分页查询参数封装

#### 配置类 (Config)
- `GlobalExceptionHandler.java` - 全局异常处理器
- `MyBatisFlexConfig.java` - MyBatis-Flex框架配置

#### 启动类
- `FlexDemoApplication.java` - Spring Boot应用启动类

### 🌐 前端文件

| 文件 | 用途 | 说明 |
|------|------|------|
| `index.html` | 测试页面 | 提供完整的API测试界面 |

## 📊 技术栈文件对应关系

| 技术 | 相关文件 |
|------|----------|
| **Spring Boot** | `FlexDemoApplication.java`, `application.yml`, `pom.xml` |
| **MyBatis-Flex** | `*Mapper.java`, `MyBatisFlexConfig.java`, `pom.xml` |
| **HuTool** | `UserService.java`, `pom.xml` |
| **Apache POI** | `UserService.java`, `pom.xml` |
| **MySQL** | `application.yml`, `pom.xml` |
| **Maven** | `pom.xml` |
| **Git** | `.gitignore`, `.gitattributes`, `git-init.ps1` |

## 🔍 文件大小统计

| 类型 | 数量 | 说明 |
|------|------|------|
| Java源文件 | 16个 | 包含实体、服务、控制器等 |
| 配置文件 | 4个 | Maven、Spring Boot、Git配置 |
| 文档文件 | 3个 | README、Git指南、文件清单 |
| 脚本文件 | 5个 | 测试和Git管理脚本 |
| 前端文件 | 1个 | HTML测试页面 |
| **总计** | **29个** | 不包含构建生成的文件 |

## 📝 重要说明

1. **target/** 目录包含Maven构建生成的文件，已被Git忽略
2. **application-local.yml** 等本地配置文件需要手动创建
3. **数据库初始化脚本** 建议放在 `src/main/resources/db/` 目录
4. **日志文件** 会在运行时生成，已被Git忽略
5. **上传文件** 会保存在 `uploads/` 目录，已被Git忽略

## 🚀 下一步建议

1. 根据实际需求修改数据库连接配置
2. 添加更多业务实体和功能
3. 完善单元测试和集成测试
4. 添加API文档生成（如Swagger）
5. 配置CI/CD流水线
6. 添加Docker支持
